import { HttpError, OpenNotificationParams } from '@refinedev/core';
import axios from 'axios';
import { AccessPolicy, IUserIdentity } from 'relic-ui';
import { getNodeServicesApi } from '../../utils/app-config';
import { notificationProvider } from '../notification-provider';

import { getAppName } from 'src/utils/app-config';

import { IActiveLogin } from 'src/types';

const authAxiosInstance = axios.create();

authAxiosInstance.interceptors.response.use(
  response => response,
  error => {
    const customError: HttpError = {
      ...error,
      message:
        error.response?.data?.message ||
        error.response?.message ||
        error?.message ||
        'An unknown error occurred',
      statusCode: error.response?.status,
    };

    return Promise.reject(customError);
  },
);

export const getActiveLogin = (): IActiveLogin => {
  const appName: string = getAppName();
  let activeLogin: IActiveLogin;
  //Use different storage locations for activeLogin based on current app
  if (appName === 'relic-ai') {
    activeLogin = JSON.parse(localStorage.getItem('activeLoginEHR') as string);
  } else {
    activeLogin = JSON.parse(localStorage.getItem('activeLogin') as string);
  }
  if (!activeLogin) {
    return {
      provider: '',
      accessToken: '',
    };
  }
  const userIdentity = JSON.parse(
    localStorage.getItem('userIdentity') as string,
  );
  activeLogin.name = activeLogin.name ?? userIdentity?.portalIdentity.name;
  return activeLogin;
};

/**
 * Generates a signed Id Token Hint for Azure AD B2C.
 *
 * @param {string} type - The type of identifier. Must be either 'email' or 'phone'.
 * @param {string} id - The value of the identifier. Must be a valid email address
 * for type 'email' or a phone number in the format +********** for type 'phone'.
 * @returns {Promise<string>} A promise that resolves with the signed Id Token Hint
 * as a string.
 */
export const getIdTokenHint = async (
  type: 'phone' | 'email',
  id: string,
): Promise<string> => {
  const response = await authAxiosInstance.get(
    `${getNodeServicesApi()}/api/tokens/hint/${type}/${id}`,
  );
  return response.data;
};

export function jwtDecode(token: string): any {
  if (!token) return null;

  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(
    window
      .atob(base64)
      .split('')
      .map(c => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
      .join(''),
  );

  return JSON.parse(jsonPayload);
}

export const whoami = async (
  activeLogin: IActiveLogin,
): Promise<IUserIdentity> => {
  return await authAxiosInstance
    .get(`${getNodeServicesApi()}/api/${activeLogin.provider}/whoami`, {
      headers: {
        'x-access-token': activeLogin.accessToken ?? '',
        'x-id-token': activeLogin.idToken ?? '',
        'x-organization-id': activeLogin.organizationId ?? '',
      },
    })
    .then(response => response.data);
};

export const whatsMyAccess = async (
  activeLogin: IActiveLogin,
): Promise<AccessPolicy> => {
  return await authAxiosInstance
    .get(
      `${getNodeServicesApi()}/api/${activeLogin.provider}/access-policies`,
      {
        headers: {
          'x-access-token': activeLogin.accessToken ?? '',
          'x-id-token': activeLogin.idToken ?? '',
          'x-organization-id': activeLogin.organizationId ?? '',
        },
      },
    )
    .then(response => response.data);
};

export function triggerUiNotification(
  notificationParams: OpenNotificationParams,
) {
  notificationProvider.open(notificationParams);
}

/**
 * Stores the user identity and access policy in localStorage.
 *
 * @param {IActiveLogin} activeLogin - The active login object.
 * @returns {Promise<void>} A promise that resolves when the operation is complete.
 */
export async function storeUserIdentityAndAccess(
  activeLogin: IActiveLogin,
): Promise<void> {
  const userIdentity: IUserIdentity = await whoami(activeLogin);
  userIdentity.portalIdentity.header = true;
  userIdentity.portalIdentity.sider = true;
  localStorage.setItem('userIdentity', JSON.stringify(userIdentity));
  activeLogin.organizationId = userIdentity.portalIdentity.organizationId;

  const myAccess: AccessPolicy = await whatsMyAccess(activeLogin);
  localStorage.setItem('accessPolicy', JSON.stringify(myAccess));
}

export const getProviderServicesApi = (nodeServicesApi: string): string => {
  const activeLogin = getActiveLogin();
  if (activeLogin?.provider) {
    return `${nodeServicesApi}/api/${activeLogin.provider}`;
  }
  return `${nodeServicesApi}/api`;
};

/**
 * Generates a URI based on the provided options and current URL.
 *
 * @param {boolean} _isB2C - Indicates whether the operation is for B2C (Business to Consumer).
 * @param {string} [redirectTo] - Optional custom URI to redirect to. If not provided, a default URI is determined.
 * @param {boolean} [isFallback=false] - Indicates whether the fallback URI should be used for non-B2C flows.
 * @returns {string} The computed URI based on the parameters and current URL.
 */
function getUri(
  _isB2C: boolean, // Prefix with underscore to indicate it's not used but kept for API compatibility
  redirectTo?: string,
  isFallback: boolean = false,
): string {
  const { href } = window.location; // Extract the full href of the current location
  const isLobby = href.includes('/lobby'); // Check if the current URL is in the 'lobby' context
  const isDemo = href.includes('/demo'); // Check if the current URL is in the 'demo' context

  // If a custom redirectTo is provided, use it
  if (redirectTo) return redirectTo;

  // Determine the default URI based on the app type and context (login/fallback)
  if (isLobby) {
    // Lobby App
    return isFallback ? '/lobby/login' : '/lobby/sandbox';
  } else if (isDemo) {
    // Demo App
    return isFallback ? '/demo/login' : '/demo/sandbox';
  } else {
    // Index App
    return isFallback ? '/login' : '/patients';
  }
}

/**
 * Retrieves the redirect URI for the application.
 *
 * @param {boolean} [isB2C=false] - Indicates whether the redirect is for B2C (Business to Consumer).
 * @param {string} [redirectTo] - Optional custom URI to redirect to. If not provided, a default is computed based on app type.
 * @returns {string} The redirect URI.
 */
export function getRedirectUri(
  isB2C: boolean = false,
  redirectTo?: string,
): string {
  return getUri(isB2C, redirectTo, false);
}

/**
 * Retrieves the fallback URI for the application.
 *
 * @param {boolean} [isB2C=false] - Indicates whether the fallback is for B2C (Business to Consumer).
 * @param {string} [redirectTo] - Optional custom URI to redirect to. If not provided, a default is computed based on app type.
 * @returns {string} The fallback URI.
 */
export function getFallbackUri(
  isB2C: boolean = false,
  redirectTo?: string,
): string {
  return getUri(isB2C, redirectTo, true);
}
