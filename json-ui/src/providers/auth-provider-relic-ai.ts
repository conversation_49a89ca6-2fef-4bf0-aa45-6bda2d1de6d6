import { ServerError } from '@azure/msal-browser';
import { AuthProvider, OpenNotificationParams } from '@refinedev/core';
import {
  AuthActionResponse,
  CheckResponse,
  OnErrorResponse,
} from '@refinedev/core/dist/contexts/auth/types';
import { IUserIdentity } from 'relic-ui';

import { IActiveLogin } from 'src/types';
import {
  MsalInstance,
  getActiveLogin,
  getFallbackUri,
  getIdTokenHint,
  getRedirectUri,
  isValidLogin,
  loginRequestB2c,
  logout,
  oAuthApi,
  oAuthApiB2c,
  storeUserIdentityAndAccess,
  triggerUiNotification,
} from './utils';

const getSuccessRedirectTo = () => getRedirectUri(false);
const getFailureRedirectTo = () => getFallbackUri(false);

/**
 * The `authProviderRelicAi` constant implements the `AuthProvider` interface and provides
 * authentication functionalities for Practitioners. They can login using EHR (Electronic Health Record) systems
 * or their work accounts. It includes methods for login, logout, session checking, retrieving permissions,
 * retrieving user identity, and handling errors.
 */
export const authProviderRelicAi: AuthProvider = {
  // Practitioners can also login using Phone or Email without using External Provider.
  login: async ({ id, id_token_hint, redirectTo }) => {
    try {
      if (!id && !id_token_hint) {
        throw new Error('Neither ID nor ID token hint provided');
      }

      let token = id_token_hint;

      if (!id_token_hint) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const type = emailRegex.test(id) ? 'email' : 'phone';
        token = await getIdTokenHint(type, id);
      }

      const state = Math.random().toString(36);
      localStorage.setItem('b2cAuthState', state);

      const requestData = {
        ...loginRequestB2c,
        state,
        extraQueryParameters: {
          id_token_hint: token,
        },
        ...(redirectTo
          ? { redirectUri: redirectTo }
          : { redirectUri: getSuccessRedirectTo() }),
      };

      console.log('Request Data:', requestData);
      await oAuthApiB2c(requestData);

      // Create a new response object for this handler
      const authActionResponse: AuthActionResponse = {
        success: true,
        redirectTo: getSuccessRedirectTo(),
        error: undefined,
      };
      return Promise.resolve(authActionResponse);
    } catch (error) {
      const uiError: OpenNotificationParams = {
        type: 'error',
        key: error.statusCode || 'login-error',
        message: error instanceof ServerError ? 'Server Error' : 'Login Error',
        description:
          error.message ||
          'An unknown error occurred during login. Please try again.',
      };

      triggerUiNotification(uiError);

      // Create a new response object for this handler
      let authActionResponse: AuthActionResponse;
      if (error instanceof ServerError) {
        authActionResponse = {
          success: false,
          error: undefined,
        };
      } else {
        const errorInstance = {
          ...error,
          name: 'Login Error',
          message: error.message,
        };
        authActionResponse = {
          success: false,
          error: errorInstance,
        };
      }
      return Promise.resolve(authActionResponse);
    }
  },

  logout: async () => {
    await logout();
    const authActionResponse: AuthActionResponse = {
      success: true,
      redirectTo: getFailureRedirectTo(),
    };
    return Promise.resolve(authActionResponse);
  },

  check: async () => {
    // Azure Entra OAuth uses hash instead of search in window.location
    const urlParams = new URLSearchParams(
      window.location.search || window.location.hash.substring(1),
    );

    try {
      /* If we have the URL param code then it is an OAuth. Perform OAuth and clear URL params. */
      if (urlParams.has('code')) {
        const authorization_code = urlParams.get('code') as string;
        let state = urlParams.get('state') as string;
        // Azure Entra OAuth attaches its own state with the state passed by browser.
        if (state && state.includes('|')) {
          state = state.split('|')[1];
        }
        const pccAuthState = localStorage.getItem('pccAuthState');
        const medplumAuthState = localStorage.getItem('medplumAuthState');
        const entraAuthState = localStorage.getItem('entraAuthState');
        const b2cAuthState = localStorage.getItem('b2cAuthState');

        // Handle EHR providers
        if (state === pccAuthState) {
          await oAuthApi(authorization_code, 'login', { provider: 'pcc' });
        }
        if (state === medplumAuthState) {
          await oAuthApi(authorization_code, 'login', { provider: 'medplum' });
        }
        // Handle Entra auth response
        if (state === entraAuthState) {
          // Handle Entra auth response
          const entraClient = await MsalInstance.getClient('entra');
          const authResponse = await entraClient.handleRedirectPromise();
          if (authResponse) {
            const activeLogin = await MsalInstance.handleAuthResponse(
              authResponse,
              'entra',
            );
            localStorage.setItem('activeLoginEHR', JSON.stringify(activeLogin));
          }
        }
        if (state === b2cAuthState) {
          // Handle Entra auth response
          const b2cClient = await MsalInstance.getClient('b2c');
          const authResponse = await b2cClient.handleRedirectPromise();
          if (authResponse) {
            const activeLogin = await MsalInstance.handleAuthResponse(
              authResponse,
              'b2c',
            );
            localStorage.setItem('activeLoginEHR', JSON.stringify(activeLogin));
          }
        }

        const activeLogin: IActiveLogin = getActiveLogin();
        await storeUserIdentityAndAccess(activeLogin);
      }

      /* Now, check if the user is logged in */
      const activeLogin: IActiveLogin = getActiveLogin();
      if (
        activeLogin &&
        activeLogin?.accessToken &&
        isValidLogin(activeLogin)
      ) {
        // Create a new response object for this handler
        const checkResponse: CheckResponse = {
          authenticated: true,
          error: undefined,
        };
        return Promise.resolve(checkResponse);
      } else {
        throw new Error('No active login found. Kindly re-login.');
      }
    } catch (error) {
      const uiError: OpenNotificationParams = {
        type: 'error',
        key: error.statusCode,
        message: error.name,
        description: error.message,
      };
      const errorStatus =
        error.status ?? error.statusCode ?? error.response?.status;
      if (
        errorStatus === '401' ||
        errorStatus === 401 ||
        errorStatus === '400' ||
        errorStatus === 400
      ) {
        await logout();
        triggerUiNotification(uiError);
        const checkResponse: CheckResponse = {
          authenticated: false,
          logout: true,
          redirectTo: getFailureRedirectTo(),
          error,
        };
        return Promise.resolve(checkResponse);
      }
      const checkResponse: CheckResponse = {
        authenticated: false,
        logout: false,
        redirectTo: getFailureRedirectTo(),
        error,
      };
      return Promise.resolve(checkResponse);
    }
  },

  getPermissions: async () => null,

  getIdentity: async () => {
    const activeLogin: IActiveLogin = getActiveLogin();
    const userIdentity = JSON.parse(
      localStorage.getItem('userIdentity') as string,
    ) as IUserIdentity;
    if (activeLogin && userIdentity) {
      return {
        id: activeLogin.id,
        name: activeLogin.name,
        userIdentity: userIdentity,
        accessToken: activeLogin.accessToken,
        idToken: activeLogin.idToken,
        organizationId: activeLogin.organizationId,
      };
    }
    await logout();
    // Create a new response object for this handler
    const checkResponse: CheckResponse = {
      authenticated: false,
      redirectTo: getFailureRedirectTo(),
    };
    return Promise.resolve(checkResponse);
  },

  onError: async error => {
    try {
      const errorStatus =
        error.status ?? error.statusCode ?? error.response?.status;
      let onErrorResponse: OnErrorResponse;
      if (errorStatus === '401' || errorStatus === 401) {
        onErrorResponse = {
          logout: true,
          redirectTo: getFailureRedirectTo(),
          error,
        };
      } else {
        onErrorResponse = {
          logout: false,
          redirectTo: window.location.pathname + window.location.search,
          error,
        };
      }
      return Promise.resolve(onErrorResponse);
    } catch (e) {
      // fallback error response
      const onErrorResponse: OnErrorResponse = {
        logout: true,
        redirectTo: getFailureRedirectTo(),
        error: e,
      };
      return Promise.resolve(onErrorResponse);
    }
  },
};
