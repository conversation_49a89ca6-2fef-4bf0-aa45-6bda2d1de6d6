import { Authenticated, CanAccess, Refine } from '@refinedev/core';
import { ErrorComponent, RefineSnackbarProvider } from '@refinedev/mui';
import routerBindings, {
  CatchAllNavigate,
  DocumentTitleHandler,
  NavigateToResource,
  UnsavedChangesNotifier,
} from '@refinedev/react-router-v6';
import dataProvider from '@refinedev/simple-rest';
import { useTranslation } from 'react-i18next';
import { Outlet, Route, Routes } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import DashboardIcon from '@mui/icons-material/Dashboard';
import GroupIcon from '@mui/icons-material/Group';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

import { TeamsDashboard } from 'src/pages/teams/dashboard';
import { TeamsLogin } from 'src/pages/teams/login';
import { TeamsManagement } from 'src/pages/teams/management';
import {
  authProviderRelicAi as authProvider,
  relicDataProvider,
  SearchProvider,
} from 'src/providers';
import { accessControlProvider } from 'src/providers/access-control-provider';
import { notificationProvider } from 'src/providers/notification-provider';
import ThemeProvider from 'src/theme';

import { DocumentTitle } from 'src/components/_documentTitle';
import { OrganizationIcon } from 'src/components/icon/OrganizationIcon';
import { TeamsAppLayout } from 'src/components/layout/teams';
import { ModernHeaderV2 } from 'src/components/layout/teams/header';
import { ThemedTitleV2 } from 'src/components/layout/title';
import UnAuthorized from 'src/components/refine-customs/unauthorized';

function App() {
  const { t, i18n } = useTranslation();

  const i18nProvider = {
    translate: (key: string, params: object) => t(key, params),
    changeLocale: (lang: string) => i18n.changeLanguage(lang),
    getLocale: () => i18n.language,
  };

  return (
    <ThemeProvider>
      <RefineSnackbarProvider>
        <Refine
          notificationProvider={notificationProvider}
          routerProvider={routerBindings}
          authProvider={authProvider}
          accessControlProvider={accessControlProvider}
          i18nProvider={i18nProvider}
          dataProvider={{
            default: relicDataProvider,
            json_server: dataProvider('https://relic-json-server.vercel.app'),
          }}
          resources={[
            {
              name: 'dashboard',
              meta: {
                label: t('teams.dashboard'),
                icon: <DashboardIcon />,
              },
              list: '/dashboard',
            },
            {
              name: 'teams',
              meta: {
                label: t('teams.management'),
                icon: <GroupIcon />,
              },
              list: '/teams',
            },
          ]}
          options={{
            syncWithLocation: true,
            useNewQueryKeys: true,
            projectId: 'uvSWGo-A848GS-kqC6S9',
            reactQuery: {
              clientConfig: {
                defaultOptions: {
                  queries: {
                    staleTime: 5 * 60 * 1000,
                    retry: false,
                  },
                },
              },
            },
          }}
        >
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <SearchProvider>
              <Routes>
                <Route
                  element={
                    <Authenticated
                      key="authenticated-inner"
                      fallback={<CatchAllNavigate to="/login" />}
                    >
                      <TeamsAppLayout
                        Title={({ collapsed }) => (
                          <ThemedTitleV2
                            collapsed={collapsed}
                            icon={<OrganizationIcon />}
                            text="Teams Portal"
                          />
                        )}
                        Header={() => <ModernHeaderV2 sticky />}
                      >
                        <CanAccess fallback={<UnAuthorized />}>
                          <Outlet />
                        </CanAccess>
                      </TeamsAppLayout>
                    </Authenticated>
                  }
                >
                  <Route
                    index
                    element={<NavigateToResource resource="dashboard" />}
                  />
                  <Route path="dashboard">
                    <Route index element={<TeamsDashboard />} />
                  </Route>
                  <Route path="teams">
                    <Route index element={<TeamsManagement />} />
                  </Route>

                  <Route path="*" element={<ErrorComponent />} />
                </Route>
                <Route
                  element={
                    <Authenticated
                      key="authenticated-outer"
                      fallback={<Outlet />}
                    >
                      <NavigateToResource />
                    </Authenticated>
                  }
                >
                  <Route path="/login" element={<TeamsLogin />} />
                </Route>
              </Routes>
            </SearchProvider>
          </LocalizationProvider>

          <UnsavedChangesNotifier />
          <DocumentTitleHandler handler={DocumentTitle} />
          <ToastContainer />
        </Refine>
      </RefineSnackbarProvider>
    </ThemeProvider>
  );
}

export default App;
