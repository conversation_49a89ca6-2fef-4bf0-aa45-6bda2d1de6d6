import { DEFAULT_COMPONENT_ICONS } from '@azure/communication-react';
import { initializeIcons, registerIcons } from '@fluentui/react';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';

import { LicenseInfo } from '@mui/x-license';

import { setAppName } from 'src/utils/app-config';

import App from './App';
import './i18n';

LicenseInfo.setLicenseKey(
  'b970abffbe3bf712e68edaf3dc5a9676Tz0xMTI2MjUsRT0xNzc4Mjg0Nzk5MDAwLFM9cHJvLExNPXN1YnNjcmlwdGlvbixQVj1RMy0yMDI0LEtWPTI=',
);

// Register icons for Azure Communication Services
registerIcons({ icons: DEFAULT_COMPONENT_ICONS });
initializeIcons();

// Set app name for configuration
setAppName('teams');

const container = document.getElementById('root') as HTMLElement;
const root = createRoot(container);

root.render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>,
);
