import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { registerIcons, initializeIcons } from '@fluentui/react';
import { DEFAULT_COMPONENT_ICONS } from '@azure/communication-react';

import { LicenseInfo } from '@mui/x-license';

import { setAppName } from 'src/utils/app-config';

import { MsalInstance } from 'src/providers/utils';

import './i18n';
import App from './App';

LicenseInfo.setLicenseKey(
  'b970abffbe3bf712e68edaf3dc5a9676Tz0xMTI2MjUsRT0xNzc4Mjg0Nzk5MDAwLFM9cHJvLExNPXN1YnNjcmlwdGlvbixQVj1RMy0yMDI0LEtWPTI=',
);

// Register icons for Azure Communication Services
registerIcons({ icons: DEFAULT_COMPONENT_ICONS });
initializeIcons();

// Set app name for configuration
setAppName('teams');

// Initialize MSAL instances
MsalInstance.initialize();

const container = document.getElementById('root') as HTMLElement;
const root = createRoot(container);

root.render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>,
);
